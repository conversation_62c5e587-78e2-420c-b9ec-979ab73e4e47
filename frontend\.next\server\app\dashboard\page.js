/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./app/dashboard/loading.tsx":
/*!***********************************!*\
  !*** ./app/dashboard/loading.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZGFzaGJvYXJkL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQTtJQUN0QixPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxhcHBcXGRhc2hib2FyZFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\my_project\\work\\hermes\\frontend\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17b65432e590\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTdiNjU0MzJlNTkwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font_sans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font/sans */ \"(rsc)/./node_modules/.pnpm/geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3/node_modules/geist/dist/sans.js\");\n/* harmony import */ var geist_font_mono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! geist/font/mono */ \"(rsc)/./node_modules/.pnpm/geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3/node_modules/geist/dist/mono.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: 'v0 App',\n    description: 'Created with v0',\n    generator: 'v0.app'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\nhtml {\n  font-family: ${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.style.fontFamily};\n  --font-sans: ${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable};\n  --font-mono: ${geist_font_mono__WEBPACK_IMPORTED_MODULE_2__.GeistMono.variable};\n}\n        `\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUMyQztBQUNBO0FBQ3JCO0FBRWYsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxXQUFXO0FBQ2IsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLOzswQkFDVCw4REFBQ0M7MEJBQ0MsNEVBQUNDOzhCQUFPLENBQUM7O2VBRUYsRUFBRVgsc0RBQVNBLENBQUNXLEtBQUssQ0FBQ0MsVUFBVSxDQUFDO2VBQzdCLEVBQUVaLHNEQUFTQSxDQUFDYSxRQUFRLENBQUM7ZUFDckIsRUFBRVosc0RBQVNBLENBQUNZLFFBQVEsQ0FBQzs7UUFFNUIsQ0FBQzs7Ozs7Ozs7Ozs7MEJBRUgsOERBQUNDOzBCQUFNUDs7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIkQ6XFxteV9wcm9qZWN0XFx3b3JrXFxoZXJtZXNcXGZyb250ZW5kXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBHZWlzdFNhbnMgfSBmcm9tICdnZWlzdC9mb250L3NhbnMnXG5pbXBvcnQgeyBHZWlzdE1vbm8gfSBmcm9tICdnZWlzdC9mb250L21vbm8nXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAndjAgQXBwJyxcbiAgZGVzY3JpcHRpb246ICdDcmVhdGVkIHdpdGggdjAnLFxuICBnZW5lcmF0b3I6ICd2MC5hcHAnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8aGVhZD5cbiAgICAgICAgPHN0eWxlPntgXG5odG1sIHtcbiAgZm9udC1mYW1pbHk6ICR7R2Vpc3RTYW5zLnN0eWxlLmZvbnRGYW1pbHl9O1xuICAtLWZvbnQtc2FuczogJHtHZWlzdFNhbnMudmFyaWFibGV9O1xuICAtLWZvbnQtbW9ubzogJHtHZWlzdE1vbm8udmFyaWFibGV9O1xufVxuICAgICAgICBgfTwvc3R5bGU+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkdlaXN0U2FucyIsIkdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImdlbmVyYXRvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsInN0eWxlIiwiZm9udEZhbWlseSIsInZhcmlhYmxlIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?df05\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/loading.tsx */ \"(rsc)/./app/dashboard/loading.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module4, \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMV9yZWFjdCU0MDE5LjEuMV9fcmVhY3QlNDAxOS4xLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDbXlfcHJvamVjdCU1QyU1Q3dvcmslNUMlNUNoZXJtZXMlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDbmV4dCU0MDE1LjIuNF9yZWFjdC1kb20lNDAxOS4xLjFfcmVhY3QlNDAxOS4xLjFfX3JlYWN0JTQwMTkuMS4xJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q215X3Byb2plY3QlNUMlNUN3b3JrJTVDJTVDaGVybWVzJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4yLjRfcmVhY3QtZG9tJTQwMTkuMS4xX3JlYWN0JTQwMTkuMS4xX19yZWFjdCU0MDE5LjEuMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMV9yZWFjdCU0MDE5LjEuMV9fcmVhY3QlNDAxOS4xLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMV9yZWFjdCU0MDE5LjEuMV9fcmVhY3QlNDAxOS4xLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMV9yZWFjdCU0MDE5LjEuMV9fcmVhY3QlNDAxOS4xLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMV9yZWFjdCU0MDE5LjEuMV9fcmVhY3QlNDAxOS4xLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40X3JlYWN0LWRvbSU0MDE5LjEuMV9yZWFjdCU0MDE5LjEuMV9fcmVhY3QlNDAxOS4xLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwWEFBdU47QUFDdk47QUFDQSxnWUFBME47QUFDMU47QUFDQSxnWUFBME47QUFDMU47QUFDQSwwYUFBZ1A7QUFDaFA7QUFDQSw4WEFBeU47QUFDek47QUFDQSxrWkFBb087QUFDcE87QUFDQSx3WkFBdU87QUFDdk87QUFDQSw0WkFBd08iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG15X3Byb2plY3RcXFxcd29ya1xcXFxoZXJtZXNcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxteV9wcm9qZWN0XFxcXHdvcmtcXFxcaGVybWVzXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG15X3Byb2plY3RcXFxcd29ya1xcXFxoZXJtZXNcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXG15X3Byb2plY3RcXFxcd29ya1xcXFxoZXJtZXNcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTUuMi40X3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxteV9wcm9qZWN0XFxcXHdvcmtcXFxcaGVybWVzXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE1LjIuNF9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sidebar */ \"(ssr)/./components/sidebar.tsx\");\n/* harmony import */ var _components_mobile_top_nav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/mobile-top-nav */ \"(ssr)/./components/mobile-top-nav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"http://113.44.46.216:3000/chat/share?shareId=ojx0r2yNoLDdh0VegHymkBAj\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-primary\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-secondary\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-muted-foreground\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-accent\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"Escape\" && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleKeyDown);\n                    window.removeEventListener(\"resize\", handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Renamed from handleAgentSelect to handleViewSelect\n        if (view === \"home\") {\n            setSelectedView(\"home\");\n            setMobileMenuOpen(false);\n            return;\n        }\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-svh bg-blue-50 flex flex-col md:flex-row overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_top_nav__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                selectedView: selectedView,\n                onViewSelect: handleViewSelect,\n                onLogout: handleLogout,\n                username: username,\n                agents: agents\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedView: selectedView,\n                onViewSelect: handleViewSelect,\n                onLogout: handleLogout,\n                username: username,\n                agents: agents,\n                mobileMenuOpen: mobileMenuOpen,\n                onToggleMobileMenu: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-y-auto min-h-0 pt-[73px] md:pt-3\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full min-h-0 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-card border-border hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-muted-foreground\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-foreground mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-10 h-10 ${stat.color} rounded-md flex items-center justify-center`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-xs font-medium ${stat.trendDirection === \"up\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                                        children: [\n                                                            stat.trendDirection === \"up\" ? \"↗\" : \"↘\",\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-2 gap-4 max-w-full flex-1 min-h-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-card border-border hover:shadow-lg transition-shadow max-w-full h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-foreground flex items-center gap-2 text-base\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能概览\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3 h-full overflow-auto flex-1\",\n                                            children: performanceData.agentPerformance.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-muted rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-foreground truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-bold text-primary\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-card border-border hover:shadow-lg transition-shadow max-w-full h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-foreground flex items-center gap-2 text-base\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"实时活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3 h-full overflow-auto flex-1\",\n                                            children: agentActivity.slice(0, 4).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 bg-muted rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-3 h-3 rounded-full ${activity.color}`\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-foreground truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: `text-xs font-medium ${activity.status === \"已完成\" ? \"text-green-600\" : activity.status === \"进行中\" ? \"text-blue-600\" : \"text-yellow-600\"}`,\n                                                                    children: activity.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: activity.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this) : (()=>{\n                    const selectedAgent = agents.find((agent)=>agent.id === selectedView);\n                    if (selectedAgent) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"h-full bg-white border-blue-200 p-0 gap-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"h-full p-0\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 animate-spin text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: [\n                                                    \"加载 \",\n                                                    selectedAgent.name,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 23\n                                }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-12 w-12 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-900 font-medium\",\n                                                        children: \"加载失败\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-600 text-sm mt-1\",\n                                                        children: [\n                                                            \"无法加载 \",\n                                                            selectedAgent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                onClick: ()=>handleViewSelect(selectedView),\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 23\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                    src: selectedAgent.url,\n                                    className: \"w-full h-full border-0 rounded-lg\",\n                                    title: selectedAgent.name,\n                                    onError: ()=>setIframeError(true),\n                                    sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 23\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-full bg-white border-blue-200 p-0 gap-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"h-full p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-16 w-16 text-blue-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                            children: \"功能模块\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-600\",\n                                            children: \"此功能正在开发中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 15\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/mobile-top-nav.tsx":
/*!***************************************!*\
  !*** ./components/mobile-top-nav.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileTopNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,ChevronDown,Home,LogOut,Menu,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MobileTopNav({ selectedView, onViewSelect, onLogout, username, agents }) {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"ai-agents\"\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const toggleSubmenu = (menuId)=>{\n        setExpandedMenus((prev)=>prev.includes(menuId) ? prev.filter((id)=>id !== menuId) : [\n                ...prev,\n                menuId\n            ]);\n    };\n    const handleMenuItemClick = (view)=>{\n        onViewSelect(view);\n        setIsMenuOpen(false) // 点击菜单项后自动收起菜单\n        ;\n    };\n    const handleBackdropClick = ()=>{\n        setIsMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-4 py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: toggleMenu,\n                            className: \"text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 min-h-[40px] min-w-[40px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `transition-transform duration-200 ${isMenuOpen ? \"rotate-90\" : \"\"}`,\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Agent 管理系统\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden fixed inset-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm\",\n                onClick: handleBackdropClick\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          md:hidden fixed top-14 left-0 right-0 z-50\n          bg-white border-b border-gray-200 shadow-lg\n          transform transition-all duration-300 ease-out\n          ${isMenuOpen ? \"translate-y-0 opacity-100 visible\" : \"-translate-y-full opacity-0 invisible\"}\n        `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[calc(100vh-56px)] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 bg-blue-50 border-b border-blue-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleMenuItemClick(\"home\"),\n                                    className: `\n                w-full flex items-center gap-3 px-4 py-3 text-sm font-medium\n                transition-all duration-200\n                ${selectedView === \"home\" ? \"bg-blue-600 text-white\" : \"text-gray-600 hover:bg-blue-50 hover:text-blue-600\"}\n              `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: `h-5 w-5 flex-shrink-0 ${selectedView === \"home\" ? \"text-white\" : \"text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"首页\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 mt-2 pt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleSubmenu(\"ai-agents\"),\n                                            className: \"w-full flex items-center justify-between gap-3 px-4 py-3 text-sm font-medium text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: `h-4 w-4 transition-transform duration-200 ${expandedMenus.includes(\"ai-agents\") ? \"rotate-180\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `overflow-hidden transition-all duration-300 ${expandedMenus.includes(\"ai-agents\") ? \"max-h-96 opacity-100\" : \"max-h-0 opacity-0\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 mt-1\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleMenuItemClick(agent.id),\n                                                        className: `\n                        w-full flex items-center gap-3 px-6 py-3 text-sm font-medium\n                        transition-all duration-200\n                        ${selectedView === agent.id ? \"bg-blue-600 text-white\" : \"text-gray-600 hover:bg-blue-50 hover:text-blue-600\"}\n                      `,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-2 h-2 rounded-full flex-shrink-0 ${agent.status === \"在线\" ? \"bg-green-500\" : agent.status === \"忙碌\" ? \"bg-yellow-500\" : \"bg-gray-400\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 mt-2 pt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            onLogout();\n                                            setIsMenuOpen(false);\n                                        },\n                                        className: \"w-full flex items-center gap-3 px-4 py-3 text-sm font-medium text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_ChevronDown_Home_LogOut_Menu_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"退出登录\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\mobile-top-nav.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/mobile-top-nav.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,ChevronLeft,ChevronRight,Home,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Sidebar({ selectedView, onViewSelect, onLogout, username, agents, mobileMenuOpen, onToggleMobileMenu, onTouchStart, onTouchMove, onTouchEnd }) {\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleSubmenu = (menuId)=>{\n        setExpandedMenus((prev)=>prev.includes(menuId) ? prev.filter((id)=>id !== menuId) : [\n                ...prev,\n                menuId\n            ]);\n    };\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        onToggleMobileMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: `\n          hidden md:flex\n          bg-white\n          border-r border-gray-200\n          flex-col\n          transition-all duration-300 ease-out\n          ${sidebarCollapsed ? \"w-16\" : \"w-64\"}\n          h-screen\n        `,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `border-b border-gray-200 ${sidebarCollapsed ? \"p-2\" : \"p-4\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center mb-3 ${sidebarCollapsed ? \"justify-center\" : \"justify-between\"}`,\n                            children: [\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Agent 管理系统\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: toggleSidebar,\n                                    className: \"text-gray-500 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200\",\n                                    children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 74\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-3 group relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 p-3 bg-blue-50 rounded-lg mb-3 border border-blue-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: username || \"手系 Agent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"管理员\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: `flex-1 ${sidebarCollapsed ? \"p-2 overflow-visible\" : \"p-4 overflow-y-auto\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onViewSelect(\"home\"),\n                                        className: `\n                  w-full flex items-center rounded-lg text-sm font-medium\n                  transition-all duration-200 min-h-[44px]\n                  ${sidebarCollapsed ? \"justify-center px-3 py-3\" : \"gap-3 px-3 py-3\"}\n                  ${selectedView === \"home\" ? \"bg-blue-600 text-white shadow-sm\" : \"text-gray-600 hover:bg-blue-50 hover:text-blue-600\"}\n                `,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: `h-5 w-5 flex-shrink-0 ${selectedView === \"home\" ? \"text-white\" : \"text-gray-500\"}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"首页\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 39\n                                            }, this),\n                                            !sidebarCollapsed && selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"首页\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative\",\n                                children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\n                      transition-all duration-200 min-h-[44px] px-3 py-3 cursor-default\n                      ${agents.some((agent)=>selectedView === agent.id) ? \"bg-blue-600 text-white shadow-sm\" : \"text-gray-600 hover:bg-blue-50 hover:text-blue-600\"}\n                    `,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: `h-5 w-5 flex-shrink-0 ${agents.some((agent)=>selectedView === agent.id) ? \"text-white\" : \"text-gray-500\"}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg shadow-xl w-[220px] py-2 relative overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-full top-0 w-2 h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-[-6px] top-4 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-1 max-h-[200px] overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-0.5\",\n                                                            children: [\n                                                                agents.slice(0, 6).map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>onViewSelect(agent.id),\n                                                                        className: `\n                                w-full flex items-center gap-3 px-3 py-2 text-sm font-medium\n                                transition-all duration-200 hover:bg-blue-50 rounded-md mx-1\n                                ${selectedView === agent.id ? \"bg-blue-600 text-white shadow-sm\" : \"text-gray-700 hover:text-blue-600\"}\n                              `,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: `w-2 h-2 rounded-full flex-shrink-0 ${agent.status === \"在线\" ? \"bg-green-500\" : agent.status === \"忙碌\" ? \"bg-yellow-500\" : \"bg-gray-400\"}`\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                                lineNumber: 186,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"truncate flex-1 text-left\",\n                                                                                children: agent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                                lineNumber: 195,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-white rounded-full flex-shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                                lineNumber: 197,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, agent.id, true, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 29\n                                                                    }, this)),\n                                                                agents.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-1 text-xs text-gray-500 text-center truncate\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        agents.length - 6,\n                                                                        \" 更多...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleSubmenu(\"ai-agents\"),\n                                            className: \"w-full flex items-center justify-between gap-3 px-3 py-3 text-sm font-medium text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: `h-3 w-3 transition-transform duration-200 ${expandedMenus.includes(\"ai-agents\") ? \"rotate-180\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `overflow-hidden transition-all duration-300 ${expandedMenus.includes(\"ai-agents\") ? \"max-h-96 opacity-100\" : \"max-h-0 opacity-0\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5 mt-1\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onViewSelect(agent.id),\n                                                        className: `\n                            w-full flex items-center rounded-lg text-sm font-medium\n                            transition-all duration-200 min-h-[40px] gap-3 px-4 py-2.5 ml-2\n                            ${selectedView === agent.id ? \"bg-blue-600 text-white shadow-sm\" : \"text-gray-600 hover:bg-blue-50 hover:text-blue-600\"}\n                          `,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-2 h-2 rounded-full flex-shrink-0 ${agent.status === \"在线\" ? \"bg-green-500\" : agent.status === \"忙碌\" ? \"bg-yellow-500\" : \"bg-gray-400\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 57\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `border-t border-gray-200 ${sidebarCollapsed ? \"p-2\" : \"p-4\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                onClick: onLogout,\n                                className: `\n                w-full text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg\n                transition-all duration-200 min-h-[44px]\n                ${sidebarCollapsed ? \"justify-center px-3 py-3\" : \"justify-start px-3 py-3\"}\n              `,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_ChevronLeft_ChevronRight_Home_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 text-sm\",\n                                        children: \"退出登录\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"退出登录\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\sidebar.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.10_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcbXlfcHJvamVjdFxcd29ya1xcaGVybWVzXFxmcm9udGVuZFxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteV9wcm9qZWN0JTVDJTVDd29yayU1QyU1Q2hlcm1lcyU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFxRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlfcHJvamVjdFxcXFx3b3JrXFxcXGhlcm1lc1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_rea_daef5b4904953415c8db3dac047d51b3%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmy_project%5C%5Cwork%5C%5Chermes%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/geist@1.4.2_next@15.2.4_rea_daef5b4904953415c8db3dac047d51b3","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/lucide-react@0.454.0_react@19.1.1","vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@19.1.10_react@19.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@radix-ui+react-compose-ref_624bb303a1a4ee376c6bbc8b6a016c46","vendor-chunks/clsx@2.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmy_project%5Cwork%5Chermes%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();