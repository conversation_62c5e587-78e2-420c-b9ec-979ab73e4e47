"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var _components_mobile_top_nav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/mobile-top-nav */ \"(app-pages-browser)/./components/mobile-top-nav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"http://113.44.46.216:3000/chat/share?shareId=ojx0r2yNoLDdh0VegHymkBAj\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-primary\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-secondary\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-muted-foreground\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-accent\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === \"Escape\" && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleKeyDown);\n                    window.removeEventListener(\"resize\", handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Renamed from handleAgentSelect to handleViewSelect\n        if (view === \"home\") {\n            setSelectedView(\"home\");\n            setMobileMenuOpen(false);\n            return;\n        }\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-svh bg-blue-50 flex flex-col md:flex-row overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_top_nav__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                selectedView: selectedView,\n                onViewSelect: handleViewSelect,\n                onLogout: handleLogout,\n                username: username,\n                agents: agents\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedView: selectedView,\n                onViewSelect: handleViewSelect,\n                onLogout: handleLogout,\n                username: username,\n                agents: agents,\n                mobileMenuOpen: mobileMenuOpen,\n                onToggleMobileMenu: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-y-auto min-h-0 pt-[73px] md:pt-3\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full min-h-0 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-card border-border hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-muted-foreground\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-foreground mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-md flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === \"up\" ? \"text-green-600\" : \"text-red-600\"),\n                                                        children: [\n                                                            stat.trendDirection === \"up\" ? \"↗\" : \"↘\",\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-2 gap-4 max-w-full flex-1 min-h-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-card border-border hover:shadow-lg transition-shadow max-w-full h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-foreground flex items-center gap-2 text-base\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能概览\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3 h-full overflow-auto flex-1\",\n                                            children: performanceData.agentPerformance.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-muted rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-foreground truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-bold text-primary\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-card border-border hover:shadow-lg transition-shadow max-w-full h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-foreground flex items-center gap-2 text-base\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"实时活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3 h-full overflow-auto flex-1\",\n                                            children: agentActivity.slice(0, 4).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-muted rounded-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full \".concat(activity.color)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-foreground truncate\",\n                                                                            children: activity.agent\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground truncate\",\n                                                                            children: activity.action\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium \".concat(activity.status === \"已完成\" ? \"text-green-600\" : activity.status === \"进行中\" ? \"text-blue-600\" : \"text-yellow-600\"),\n                                                                    children: activity.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: activity.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this) : (()=>{\n                    const selectedAgent = agents.find((agent)=>agent.id === selectedView);\n                    if (selectedAgent) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"h-full bg-white border-blue-200 p-0 gap-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"h-full p-0\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 animate-spin text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: [\n                                                    \"加载 \",\n                                                    selectedAgent.name,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 23\n                                }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-12 w-12 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-900 font-medium\",\n                                                        children: \"加载失败\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-600 text-sm mt-1\",\n                                                        children: [\n                                                            \"无法加载 \",\n                                                            selectedAgent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                onClick: ()=>handleViewSelect(selectedView),\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 23\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                    src: selectedAgent.url,\n                                    className: \"w-full h-full border-0 rounded-lg\",\n                                    title: selectedAgent.name,\n                                    onError: ()=>setIframeError(true),\n                                    sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 23\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-full bg-white border-blue-200 p-0 gap-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"h-full p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-16 w-16 text-blue-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                            children: \"功能模块\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-600\",\n                                            children: \"此功能正在开发中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 15\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"IeJwljY6tGZiN8TpGfxpY7mXl3k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});