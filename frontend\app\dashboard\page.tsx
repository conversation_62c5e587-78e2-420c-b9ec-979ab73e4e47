"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Activity, Users, Loader2, AlertCircle, BarChart3 } from "lucide-react"
import Sidebar from "@/components/sidebar"
import MobileTopNav from "@/components/mobile-top-nav"

// Enhanced mock data for realistic demonstration
const agents = [
  {
    id: "agent1",
    name: "智能客服助手",
    url: "http://113.44.46.216:3000/chat/share?shareId=ojx0r2yNoLDdh0VegHymkBAj",
    type: "客服",
    status: "在线",
    lastActive: "2分钟前",
    tasksCompleted: 127,
    successRate: 98.5,
  },
  {
    id: "agent2",
    name: "数据分析专家",
    url: "https://example.com/agent2",
    type: "分析",
    status: "忙碌",
    lastActive: "刚刚",
    tasksCompleted: 89,
    successRate: 96.2,
  },
  {
    id: "agent3",
    name: "内容创作助理",
    url: "https://example.com/agent3",
    type: "创作",
    status: "在线",
    lastActive: "5分钟前",
    tasksCompleted: 156,
    successRate: 94.8,
  },
  {
    id: "agent4",
    name: "代码审查机器人",
    url: "https://example.com/agent4",
    type: "开发",
    status: "离线",
    lastActive: "1小时前",
    tasksCompleted: 73,
    successRate: 99.1,
  },
]

const dashboardStats = [
  {
    title: "总代理",
    value: "4",
    subtitle: "3个在线",
    color: "bg-primary", // Deep navy blue
    trend: "+2",
    trendDirection: "up",
  },
  {
    title: "CPU使用率",
    value: "42.8%",
    subtitle: "平均负载",
    color: "bg-secondary", // Business blue
    trend: "-5.2%",
    trendDirection: "down",
  },
  {
    title: "内存使用",
    value: "67.3%",
    subtitle: "8.2GB / 12GB",
    color: "bg-muted-foreground", // Professional gray
    trend: "+3.1%",
    trendDirection: "up",
  },
  {
    title: "今日任务",
    value: "445",
    subtitle: "已完成",
    color: "bg-accent", // Neutral accent
    trend: "+28",
    trendDirection: "up",
  },
]

const agentActivity = [
  {
    id: 1,
    agent: "智能客服助手",
    action: "处理用户咨询",
    status: "已完成",
    time: "刚刚",
    color: "bg-green-500",
    duration: "2分钟",
  },
  {
    id: 2,
    agent: "数据分析专家",
    action: "生成销售报告",
    status: "进行中",
    time: "3分钟前",
    color: "bg-blue-500",
    duration: "预计5分钟",
  },
  {
    id: 3,
    agent: "内容创作助理",
    action: "撰写产品描述",
    status: "已完成",
    time: "5分钟前",
    color: "bg-green-500",
    duration: "8分钟",
  },
  {
    id: 4,
    agent: "智能客服助手",
    action: "更新知识库",
    status: "已完成",
    time: "8分钟前",
    color: "bg-green-500",
    duration: "3分钟",
  },
  {
    id: 5,
    agent: "代码审查机器人",
    action: "代码质量检查",
    status: "等待中",
    time: "12分钟前",
    color: "bg-yellow-500",
    duration: "待处理",
  },
]

// Performance metrics for charts
const performanceData = {
  systemLoad: [
    { time: "00:00", cpu: 35, memory: 62, network: 45 },
    { time: "04:00", cpu: 28, memory: 58, network: 38 },
    { time: "08:00", cpu: 42, memory: 65, network: 52 },
    { time: "12:00", cpu: 48, memory: 71, network: 61 },
    { time: "16:00", cpu: 38, memory: 67, network: 47 },
    { time: "20:00", cpu: 33, memory: 63, network: 42 },
  ],
  agentPerformance: [
    { name: "智能客服助手", completed: 127, success: 98.5, avgTime: 3.2 },
    { name: "数据分析专家", completed: 89, success: 96.2, avgTime: 12.5 },
    { name: "内容创作助理", completed: 156, success: 94.8, avgTime: 8.7 },
    { name: "代码审查机器人", completed: 73, success: 99.1, avgTime: 15.3 },
  ],
  taskDistribution: [
    { category: "客服咨询", count: 185, percentage: 41.6 },
    { category: "数据分析", count: 89, percentage: 20.0 },
    { category: "内容创作", count: 156, percentage: 35.1 },
    { category: "代码审查", count: 15, percentage: 3.3 },
  ],
}

export default function DashboardPage() {
  const [selectedView, setSelectedView] = useState<string>("home")
  const [username, setUsername] = useState("")
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [touchStartX, setTouchStartX] = useState<number | null>(null)
  const [touchCurrentX, setTouchCurrentX] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [iframeError, setIframeError] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    const storedUsername = localStorage.getItem("username")

    if (!isLoggedIn) {
      router.push("/")
      return
    }

    if (storedUsername) {
      setUsername(storedUsername)
    }
  }, [router])

  // Enhanced mobile menu accessibility and keyboard support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    const handleResize = () => {
      // Close mobile menu when switching to desktop view
      if (window.innerWidth >= 1024 && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    window.addEventListener("resize", handleResize)

    return () => {
      document.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("resize", handleResize)
    }
  }, [mobileMenuOpen])

  const handleLogout = () => {
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("username")
    router.push("/")
  }

  // Enhanced touch handling for mobile menu
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX)
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartX === null) return
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (touchStartX === null || touchCurrentX === null) return

    const deltaX = touchCurrentX - touchStartX
    const threshold = 50 // Minimum swipe distance

    // Swipe right to open menu (when closed)
    if (deltaX > threshold && !mobileMenuOpen) {
      setMobileMenuOpen(true)
    }
    // Swipe left to close menu (when open)
    else if (deltaX < -threshold && mobileMenuOpen) {
      setMobileMenuOpen(false)
    }

    setTouchStartX(null)
    setTouchCurrentX(null)
  }

  // Close mobile menu when clicking outside
  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setMobileMenuOpen(false)
  }

  const handleViewSelect = (view: string) => {
    // Renamed from handleAgentSelect to handleViewSelect
    if (view === "home") {
      setSelectedView("home")
      setMobileMenuOpen(false)
      return
    }

    setIsLoading(true)
    setIframeError(false)
    setSelectedView(view)
    setMobileMenuOpen(false)
    setTimeout(() => setIsLoading(false), 1000)
  }

  return (
    <div className="h-svh bg-blue-50 flex flex-col md:flex-row overflow-hidden">
      {/* 移动端顶部导航栏 */}
      <MobileTopNav
        selectedView={selectedView}
        onViewSelect={handleViewSelect}
        onLogout={handleLogout}
        username={username}
        agents={agents}
      />
      
      {/* 桌面端侧边栏 */}
      <Sidebar
        selectedView={selectedView}
        onViewSelect={handleViewSelect}
        onLogout={handleLogout}
        username={username}
        agents={agents}
        mobileMenuOpen={mobileMenuOpen}
        onToggleMobileMenu={() => setMobileMenuOpen(!mobileMenuOpen)}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      />

      <main className="flex-1 p-3 lg:p-4 overflow-y-auto min-h-0 pt-[73px] md:pt-3">
        {selectedView === "home" ? (
          <div className="flex flex-col h-full min-h-0 gap-4">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3">
              {dashboardStats.map((stat, index) => (
                <Card key={index} className="bg-card border-border hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-xs font-medium text-muted-foreground">{stat.title}</p>
                        <p className="text-lg font-bold text-foreground mt-1">{stat.value}</p>
                        <p className="text-xs text-muted-foreground">{stat.subtitle}</p>
                      </div>
                      <div className={`w-10 h-10 ${stat.color} rounded-md flex items-center justify-center`}>
                        <div className="w-5 h-5 bg-white rounded opacity-80"></div>
                      </div>
                    </div>
                    {stat.trend && (
                      <div className="mt-2 flex items-center">
                        <span
                          className={`text-xs font-medium ${
                            stat.trendDirection === "up" ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {stat.trendDirection === "up" ? "↗" : "↘"} {stat.trend}
                        </span>
                        <span className="text-xs text-muted-foreground ml-1">vs 昨天</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 max-w-full flex-1 min-h-0">
              <Card className="bg-card border-border hover:shadow-lg transition-shadow max-w-full h-full flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="text-foreground flex items-center gap-2 text-base">
                    <BarChart3 className="h-5 w-5 text-primary" />
                    Agent 性能概览
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 h-full overflow-auto flex-1">
                  {performanceData.agentPerformance.map((agent, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-md">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{agent.name}</p>
                        <p className="text-xs text-muted-foreground">成功率: {agent.success}%</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-primary">{agent.completed}</p>
                        <p className="text-xs text-muted-foreground">任务</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card className="bg-card border-border hover:shadow-lg transition-shadow max-w-full h-full flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="text-foreground flex items-center gap-2 text-base">
                    <Activity className="h-5 w-5 text-primary" />
                    实时活动
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 h-full overflow-auto flex-1">
                  {agentActivity.slice(0, 4).map((activity) => (
                    <div key={activity.id} className="flex items-center gap-3 p-3 bg-muted rounded-md">
                      <div className={`w-3 h-3 rounded-full ${activity.color}`}></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{activity.agent}</p>
                        <p className="text-xs text-muted-foreground truncate">{activity.action}</p>
                      </div>
                      <div className="text-right">
                        <p
                          className={`text-xs font-medium ${
                            activity.status === "已完成"
                              ? "text-green-600"
                              : activity.status === "进行中"
                                ? "text-blue-600"
                                : "text-yellow-600"
                          }`}
                        >
                          {activity.status}
                        </p>
                        <p className="text-xs text-muted-foreground">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* 移除“Agent 状态总览”区块以避免首页超出一屏 */}
          </div>
        ) : (
          (() => {
            const selectedAgent = agents.find((agent) => agent.id === selectedView)
            if (selectedAgent) {
              return (
                <Card className="h-full bg-white border-blue-200 p-0 gap-0">
                  <CardContent className="h-full p-0">
                    {isLoading ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="flex flex-col items-center gap-4">
                          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                          <p className="text-blue-600">加载 {selectedAgent.name}...</p>
                        </div>
                      </div>
                    ) : iframeError ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="flex flex-col items-center gap-4 text-center">
                          <AlertCircle className="h-12 w-12 text-red-500" />
                          <div>
                            <p className="text-blue-900 font-medium">加载失败</p>
                            <p className="text-blue-600 text-sm mt-1">无法加载 {selectedAgent.name}</p>
                          </div>
                          <Button onClick={() => handleViewSelect(selectedView)} variant="outline" size="sm">
                            重试
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <iframe
                        src={selectedAgent.url}
                        className="w-full h-full border-0 rounded-lg"
                        title={selectedAgent.name}
                        onError={() => setIframeError(true)}
                        sandbox="allow-scripts allow-same-origin allow-forms"
                      />
                    )}
                  </CardContent>
                </Card>
              )
            }

            return (
              <Card className="h-full bg-white border-blue-200 p-0 gap-0">
                <CardContent className="h-full p-0">
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center">
                      <Activity className="h-16 w-16 text-blue-400 mx-auto mb-4" />
                      <h2 className="text-xl font-semibold text-blue-900 mb-2">功能模块</h2>
                      <p className="text-blue-600">此功能正在开发中...</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })()
        )}
      </main>
    </div>
  )
}
