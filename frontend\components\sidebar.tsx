"use client"

import type React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, User, Menu, X, ChevronLeft, ChevronRight, Home, Bot, ChevronDown } from "lucide-react"

interface Agent {
  id: string
  name: string
  url: string
  type: string
  status: string
  lastActive: string
  tasksCompleted: number
  successRate: number
}

interface SidebarProps {
  selectedView: string
  onViewSelect: (view: string) => void
  onLogout: () => void
  username: string
  agents: Agent[]
  mobileMenuOpen: boolean
  onToggleMobileMenu: () => void
  onTouchStart: (e: React.TouchEvent) => void
  onTouchMove: (e: React.TouchEvent) => void
  onTouchEnd: () => void
}

export default function Sidebar({
  selectedView,
  onViewSelect,
  onLogout,
  username,
  agents,
  mobileMenuOpen,
  onToggleMobileMenu,
  onTouchStart,
  onTouchMove,
  onTouchEnd,
}: SidebarProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)
  const [expandedMenus, setExpandedMenus] = useState<string[]>([])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleSubmenu = (menuId: string) => {
    setExpandedMenus((prev) => (prev.includes(menuId) ? prev.filter((id) => id !== menuId) : [...prev, menuId]))
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleMobileMenu()
  }

  return (
    <>
      {/* 在移动端完全隐藏侧边栏 */}
      <aside
        className={`
          hidden md:flex
          bg-white
          border-r border-gray-200
          flex-col
          transition-all duration-300 ease-out
          ${sidebarCollapsed ? "w-16" : "w-64"}
          h-screen
        `}
      >
        <div className={`border-b border-gray-200 ${sidebarCollapsed ? "p-2" : "p-4"}`}>
          <div className={`flex items-center mb-3 ${sidebarCollapsed ? "justify-center" : "justify-between"}`}>
            {!sidebarCollapsed && <h1 className="text-lg font-semibold text-gray-900">Agent 管理系统</h1>}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="text-gray-500 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200"
            >
              {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>

          {sidebarCollapsed ? (
            <div className="flex justify-center mb-3 group relative">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50">
                <div className="bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white">
                  <p className="text-sm font-medium text-gray-700">{username || "手系 Agent"}</p>
                  <p className="text-xs text-gray-500">管理员</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg mb-3 border border-blue-100">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{username || "手系 Agent"}</p>
                <p className="text-xs text-gray-500">管理员</p>
              </div>
            </div>
          )}
        </div>

        <nav className={`flex-1 ${sidebarCollapsed ? "p-2 overflow-visible" : "p-4 overflow-y-auto"}`}>
          <div className="space-y-1">
            {/* Dashboard Home */}
            <div className="group relative">
              <button
                onClick={() => onViewSelect("home")}
                className={`
                  w-full flex items-center rounded-lg text-sm font-medium
                  transition-all duration-200 min-h-[44px]
                  ${sidebarCollapsed ? "justify-center px-3 py-3" : "gap-3 px-3 py-3"}
                  ${
                    selectedView === "home"
                      ? "bg-blue-600 text-white shadow-sm"
                      : "text-gray-600 hover:bg-blue-50 hover:text-blue-600"
                  }
                `}
              >
                <Home className={`h-5 w-5 flex-shrink-0 ${selectedView === "home" ? "text-white" : "text-gray-500"}`} />
                {!sidebarCollapsed && <span>首页</span>}
                {!sidebarCollapsed && selectedView === "home" && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full" />
                )}
              </button>

              {sidebarCollapsed && (
                <div className="absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto">
                  <div className="bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white">
                    <p className="text-sm font-medium text-gray-700">首页</p>
                  </div>
                </div>
              )}
            </div>

            {/* AI Agents Section */}
            <div className="group relative">
              {sidebarCollapsed ? (
                <>
                  <div
                    className={`
                      w-full flex items-center justify-center rounded-lg text-sm font-medium
                      transition-all duration-200 min-h-[44px] px-3 py-3 cursor-default
                      ${
                        agents.some((agent) => selectedView === agent.id)
                          ? "bg-blue-600 text-white shadow-sm"
                          : "text-gray-600 hover:bg-blue-50 hover:text-blue-600"
                      }
                    `}
                  >
                    <Bot
                      className={`h-5 w-5 flex-shrink-0 ${agents.some((agent) => selectedView === agent.id) ? "text-white" : "text-gray-500"}`}
                    />
                  </div>

                  <div className="absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto">
                    <div className="bg-white border border-gray-200 rounded-lg shadow-xl w-[220px] py-2 relative overflow-hidden">
                      {/* Invisible bridge area to prevent tooltip disappearing */}
                      <div className="absolute right-full top-0 w-2 h-full"></div>
                      <div className="absolute left-[-6px] top-4 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-white"></div>
                      <div className="py-1 max-h-[200px] overflow-hidden">
                        <div className="space-y-0.5">
                          {agents.slice(0, 6).map((agent) => (
                            <button
                              key={agent.id}
                              onClick={() => onViewSelect(agent.id)}
                              className={`
                                w-full flex items-center gap-3 px-3 py-2 text-sm font-medium
                                transition-all duration-200 hover:bg-blue-50 rounded-md mx-1
                                ${
                                  selectedView === agent.id
                                    ? "bg-blue-600 text-white shadow-sm"
                                    : "text-gray-700 hover:text-blue-600"
                                }
                              `}
                            >
                              <div
                                className={`w-2 h-2 rounded-full flex-shrink-0 ${
                                  agent.status === "在线"
                                    ? "bg-green-500"
                                    : agent.status === "忙碌"
                                      ? "bg-yellow-500"
                                      : "bg-gray-400"
                                }`}
                              ></div>
                              <span className="truncate flex-1 text-left">{agent.name}</span>
                              {selectedView === agent.id && (
                                <div className="w-2 h-2 bg-white rounded-full flex-shrink-0" />
                              )}
                            </button>
                          ))}
                          {agents.length > 6 && (
                            <div className="px-3 py-1 text-xs text-gray-500 text-center truncate">
                              +{agents.length - 6} 更多...
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                    <button
                      onClick={() => toggleSubmenu("ai-agents")}
                      className="w-full flex items-center justify-between gap-3 px-3 py-3 text-sm font-medium text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
                    >
                      <div className="flex items-center gap-2">
                        <Bot className="h-5 w-5" />
                        AI 专家
                      </div>
                      <ChevronDown
                        className={`h-3 w-3 transition-transform duration-200 ${
                          expandedMenus.includes("ai-agents") ? "rotate-180" : ""
                        }`}
                      />
                  </button>

                  <div
                    className={`overflow-hidden transition-all duration-300 ${
                      expandedMenus.includes("ai-agents") ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                    }`}
                  >
                    <div className="space-y-0.5 mt-1">
                      {agents.map((agent) => (
                        <button
                          key={agent.id}
                          onClick={() => onViewSelect(agent.id)}
                          className={`
                            w-full flex items-center rounded-lg text-sm font-medium
                            transition-all duration-200 min-h-[40px] gap-3 px-4 py-2.5 ml-2
                            ${
                              selectedView === agent.id
                                ? "bg-blue-600 text-white shadow-sm"
                                : "text-gray-600 hover:bg-blue-50 hover:text-blue-600"
                            }
                          `}
                        >
                          <div
                            className={`w-2 h-2 rounded-full flex-shrink-0 ${
                              agent.status === "在线"
                                ? "bg-green-500"
                                : agent.status === "忙碌"
                                  ? "bg-yellow-500"
                                  : "bg-gray-400"
                            }`}
                          ></div>
                          <span className="truncate">{agent.name}</span>
                          {selectedView === agent.id && <div className="ml-auto w-2 h-2 bg-white rounded-full" />}
                        </button>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </nav>

        <div className={`border-t border-gray-200 ${sidebarCollapsed ? "p-2" : "p-4"}`}>
          <div className="group relative">
            <Button
              variant="ghost"
              onClick={onLogout}
              className={`
                w-full text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg
                transition-all duration-200 min-h-[44px]
                ${sidebarCollapsed ? "justify-center px-3 py-3" : "justify-start px-3 py-3"}
              `}
            >
              <LogOut className="h-5 w-5" />
              {!sidebarCollapsed && <span className="ml-3 text-sm">退出登录</span>}
            </Button>

            {sidebarCollapsed && (
              <div className="absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out z-50 group-hover:pointer-events-auto">
                <div className="bg-white border border-gray-200 rounded-lg shadow-xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-white">
                  <p className="text-sm font-medium text-gray-700">退出登录</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>
    </>
  )
}
